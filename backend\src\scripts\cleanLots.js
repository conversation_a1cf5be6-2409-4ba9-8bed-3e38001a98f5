/**
 * Script pour nettoyer les lots orphelins et vérifier l'état de la base de données
 * Ce script identifie et supprime les lots qui n'ont pas de vétérinaire assigné
 * ou qui ont des données incohérentes
 */

// Charger les variables d'environnement
require('dotenv').config();

// Importer les dépendances
const mongoose = require('mongoose');
const Lot = require('../models/Lot');
const Veterinaire = require('../models/Veterinaire');
const Pecheur = require('../models/Pecheur');
const Maryeur = require('../models/Maryeur');

// Connexion à la base de données
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => {
  console.log('✅ Connecté à la base de données MongoDB');
  cleanLots();
})
.catch(err => {
  console.error('❌ Erreur de connexion à la base de données:', err);
  process.exit(1);
});

/**
 * Fonction principale pour nettoyer les lots
 */
async function cleanLots() {
  try {
    console.log('\n🧹 === NETTOYAGE DES LOTS ===\n');
    
    // 1. Analyser l'état actuel des lots
    await analyzeLots();
    
    // 2. Nettoyer les lots orphelins
    await cleanOrphanLots();
    
    // 3. Vérifier les lots en attente par vétérinaire
    await checkPendingLotsByVeterinaire();
    
    console.log('\n✅ Nettoyage terminé avec succès !');
    
  } catch (error) {
    console.error('❌ Erreur lors du nettoyage:', error);
  } finally {
    // Fermer la connexion à la base de données
    await mongoose.connection.close();
    console.log('🔌 Connexion à MongoDB fermée');
    process.exit(0);
  }
}

/**
 * Analyser l'état actuel des lots
 */
async function analyzeLots() {
  console.log('📊 === ANALYSE DES LOTS ===');
  
  const totalLots = await Lot.countDocuments();
  console.log(`📦 Total des lots: ${totalLots}`);
  
  const lotsEnAttente = await Lot.countDocuments({
    test: false,
    isValidated: false
  });
  console.log(`⏳ Lots en attente (test: false, isValidated: false): ${lotsEnAttente}`);
  
  const lotsSansVeterinaire = await Lot.countDocuments({
    test: false,
    isValidated: false,
    $or: [
      { veterinaire: { $exists: false } },
      { veterinaire: null }
    ]
  });
  console.log(`🚫 Lots en attente SANS vétérinaire assigné: ${lotsSansVeterinaire}`);
  
  const lotsSansPecheur = await Lot.countDocuments({
    $or: [
      { user: { $exists: false } },
      { user: null }
    ]
  });
  console.log(`🚫 Lots SANS pêcheur assigné: ${lotsSansPecheur}`);
  
  const lotsValides = await Lot.countDocuments({
    test: true,
    isValidated: true
  });
  console.log(`✅ Lots validés: ${lotsValides}`);
  
  const lotsRefuses = await Lot.countDocuments({
    test: true,
    isValidated: false
  });
  console.log(`❌ Lots refusés: ${lotsRefuses}`);
  
  console.log('');
}

/**
 * Nettoyer les lots orphelins
 */
async function cleanOrphanLots() {
  console.log('🧹 === NETTOYAGE DES LOTS ORPHELINS ===');
  
  // Trouver les lots sans vétérinaire assigné qui sont en attente
  const orphanLots = await Lot.find({
    test: false,
    isValidated: false,
    $or: [
      { veterinaire: { $exists: false } },
      { veterinaire: null }
    ]
  });
  
  console.log(`🔍 Lots orphelins trouvés: ${orphanLots.length}`);
  
  if (orphanLots.length > 0) {
    console.log('\n📋 Détails des lots orphelins:');
    for (const lot of orphanLots) {
      console.log(`  - ${lot.identifiant} | Espèce: ${lot.especeNom} | Pêcheur: ${lot.user || 'N/A'}`);
    }
    
    // Demander confirmation avant suppression
    console.log('\n⚠️  Ces lots seront supprimés car ils n\'ont pas de vétérinaire assigné.');
    console.log('💡 Dans un workflow correct, chaque lot doit avoir un vétérinaire assigné dès la création.');
    
    // Supprimer les lots orphelins
    const deleteResult = await Lot.deleteMany({
      test: false,
      isValidated: false,
      $or: [
        { veterinaire: { $exists: false } },
        { veterinaire: null }
      ]
    });
    
    console.log(`🗑️  ${deleteResult.deletedCount} lots orphelins supprimés`);
  } else {
    console.log('✅ Aucun lot orphelin trouvé');
  }
  
  console.log('');
}

/**
 * Vérifier les lots en attente par vétérinaire
 */
async function checkPendingLotsByVeterinaire() {
  console.log('👨‍⚕️ === LOTS EN ATTENTE PAR VÉTÉRINAIRE ===');
  
  // Récupérer tous les vétérinaires
  const veterinaires = await Veterinaire.find({}, 'nom prenom email');
  console.log(`👥 Nombre total de vétérinaires: ${veterinaires.length}`);
  
  for (const vet of veterinaires) {
    const pendingLots = await Lot.countDocuments({
      test: false,
      isValidated: false,
      veterinaire: vet._id,
      user: { $exists: true, $ne: null },
      especeNom: { $exists: true, $ne: null },
      photo: { $exists: true, $ne: null }
    });
    
    if (pendingLots > 0) {
      console.log(`  📋 ${vet.prenom} ${vet.nom} (${vet.email}): ${pendingLots} lots en attente`);
      
      // Afficher les détails des lots
      const lots = await Lot.find({
        test: false,
        isValidated: false,
        veterinaire: vet._id,
        user: { $exists: true, $ne: null },
        especeNom: { $exists: true, $ne: null },
        photo: { $exists: true, $ne: null }
      }, 'identifiant especeNom dateSoumission').populate('user', 'nom prenom');
      
      for (const lot of lots) {
        const pecheurNom = lot.user ? `${lot.user.prenom} ${lot.user.nom}` : 'Pêcheur inconnu';
        console.log(`    - ${lot.identifiant} | ${lot.especeNom} | Pêcheur: ${pecheurNom} | ${lot.dateSoumission?.toLocaleDateString()}`);
      }
    }
  }
  
  console.log('');
}
